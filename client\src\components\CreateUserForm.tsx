import { useState } from "react";
import { TextInput, <PERSON><PERSON>, Select, Stack, Text } from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { <PERSON>con<PERSON>he<PERSON>, IconX } from "@tabler/icons-react";
import { apiClient } from "../config/axios";
import { roleValues } from "../constants";
import { useForm } from "@mantine/form";
import { rolesLabelMap } from "../constants";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import { isAxiosError } from "axios";
import validator from "validator";
import type { profileStatusDataType } from "../types";

interface Props {
	allowedRoles: ("SuperAdmin" | "Admin" | "CommunityMember")[];
	onUserCreated: (user: {
		_id: string;
		firstName: string;
		middleName?: string;
		secondName: string;
		email: string;
		mobile: string;
		role: number;
		profileStatus: profileStatusDataType;
	}) => void;
	userToEdit?: {
		_id: string;
		firstName: string;
		middleName?: string;
		secondName: string;
		email: string;
		mobile: string;
		role: number;
	} | null;
	setOpened: (opened: boolean) => void;
}

const CreateUserForm = ({
	allowedRoles,
	onUserCreated,
	setOpened,
	userToEdit,
}: Props) => {
	const [loading, setLoading] = useState(false);

	const form = useForm({
		initialValues: {
			firstName: userToEdit?.firstName || "",
			middleName: userToEdit?.middleName || "",
			secondName: userToEdit?.secondName || "",
			email: userToEdit?.email || "",
			mobile: userToEdit?.mobile || "",
			role: userToEdit
				? Object.keys(roleValues).find(
						key =>
							roleValues[key as keyof typeof roleValues] ===
							userToEdit.role
					) || ""
				: "",
		},
		transformValues: values => ({
			...values,
			firstName: values.firstName.trim(),
			middleName: values.middleName?.trim(),
			secondName: values.secondName.trim(),
			email: values.email.toLowerCase().trim(),
			mobile: values.mobile.trim(),
		}),
		validate: {
			firstName: val =>
				val.trim() === "" ? "First name is required" : null,
			secondName: val =>
				val.trim() === "" ? "Last name is required" : null,
			email: val => {
				const trimmedVal = val.trim();
				if (!val) {
					return "Email is required";
				}
				if (!validator.isEmail(trimmedVal)) {
					return "Invalid email address";
				}
				return null;
			},
			mobile: val => {
				if (!val) return "Mobile number is required";
				if (
					!validator.isMobilePhone(val, "any", { strictMode: true })
				) {
					return "Enter a valid mobile number";
				}
				return null;
			},
			role: val => (!val ? "Please select a role" : null),
		},
	});

	const handleSubmit = async (values: typeof form.values) => {
		setLoading(true);
		try {
			const roleValue =
				roleValues[values.role as keyof typeof roleValues];

			let res;
			if (userToEdit) {
				res = await apiClient.put(
					`/api/users/update-user/${userToEdit._id}`,
					{
						firstName: values.firstName,
						middleName: values.middleName,
						secondName: values.secondName,
					}
				);
				notifications.show({
					title: "Success",
					message: "User updated successfully!",
					color: "green",
					icon: <IconCheck />,
				});
			} else {
				res = await apiClient.post("/api/users/create-user", {
					firstName: values.firstName,
					middleName: values.middleName,
					secondName: values.secondName,
					email: values.email,
					mobile: values.mobile,
					role: roleValue,
				});
				notifications.show({
					title: "Success",
					message: "User created successfully!",
					color: "green",
					icon: <IconCheck />,
				});
			}

			if (onUserCreated) onUserCreated(res.data);
			form.reset();
			setOpened(false);
		} catch (err) {
			if (isAxiosError(err)) {
				notifications.show({
					title: "Unable to create user",
					message: err.response?.data?.message || "Can't create user",
					color: "red",
					icon: <IconX />,
				});
			}
		}
		setLoading(false);
	};

	return (
		<form onSubmit={form.onSubmit(handleSubmit)}>
			<Stack>
				<TextInput
					label="First Name"
					placeholder="Enter first name"
					{...form.getInputProps("firstName")}
					required
				/>
				<TextInput
					label="Middle Name"
					placeholder="Enter middle name"
					{...form.getInputProps("middleName")}
				/>
				<TextInput
					label="Last Name"
					placeholder="Enter last name"
					{...form.getInputProps("secondName")}
					required
				/>
				<TextInput
					label="Email"
					placeholder="Enter email"
					{...form.getInputProps("email")}
					required
				/>
				<Text size="sm" mt={-6} mb={-6}>
					Mobile{" "}
					<Text component="span" size="xs" c="dimmed">
						(WhatsApp active)
					</Text>
					<Text ml={2} span c="red">
						*
					</Text>
				</Text>
				<PhoneInput
					value={form.values.mobile}
					country={"in"}
					onChange={phone =>
						form.setFieldValue("mobile", `+${phone}`)
					}
					inputProps={{
						required: true,
					}}
					containerStyle={{ width: "100%" }}
					inputStyle={{
						width: "100%",
						backgroundColor: "var(--mantine-color-white)",
					}}
					dropdownStyle={{
						maxHeight: 110,
					}}
				/>
				{form.errors.mobile && (
					<Text mt={-6} mb={-6} size="xs" c="red">
						{form.errors.mobile}
					</Text>
				)}
				<Select
					label="Role"
					placeholder="Select role"
					data={allowedRoles.map(r => ({
						value: r,
						label: rolesLabelMap[r],
					}))}
					{...form.getInputProps("role")}
					required
					disabled={!!userToEdit}
				/>
				<Button type="submit" loading={loading}>
					{userToEdit ? "Update User" : "Create User"}
				</Button>
			</Stack>
		</form>
	);
};

export default CreateUserForm;
