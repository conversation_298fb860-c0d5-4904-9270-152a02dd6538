import React, { useEffect, useMemo, useRef, useState } from "react";
import {
	Button,
	Paper,
	TextInput,
	Divider,
	Group,
	Stack,
	Text,
	Image,
	ActionIcon,
	List,
	Container,
	Box,
	Textarea,
	Spoiler,
} from "@mantine/core";
import {
	Edit,
	Save,
	Upload,
	Phone,
	MapPin,
	User,
	Quote,
	Smile,
} from "lucide-react";
import {
	IconBrandInstagram,
	IconBrandLinkedin,
	IconBrandTwitter,
	IconBuilding,
	IconCheck,
	IconHash,
	IconHome,
	IconId,
	IconLink,
	IconMail,
	IconPageBreak,
	IconPlus,
	IconTrash,
	IconUserCheck,
	IconX,
} from "@tabler/icons-react";
import { useForm } from "@mantine/form";
import type { UserProfile } from "../types";
import apiClient from "../config/axios";
import { notifications } from "@mantine/notifications";
import { isAxiosError } from "axios";
import FullScreenLoader from "../components/FullScreenLoader";
import {
	IMAGE_UPLOAD_MAX_SIZE_IN_MB,
	roleLabels,
	rolesLabelMap,
} from "../constants";
import { fallbackImage } from "../utils";
import { pincodeList } from "../data/pincodes";

type ProfileProps = {
	initialValues?: Partial<UserProfile>;
	saveUrl?: string;
	isEditable?: boolean;
	onStepDone?: () => void;
	setHasUnsavedChanges?: (hasChanges: boolean) => void;
	hideCancelButton?: boolean;
	noEditInProfile?: boolean;
	fetchProfile?: () => Promise<void>;
};

const Profile = (props: ProfileProps) => {
	const fileRef = useRef<HTMLInputElement>(null);
	const [isEditing, setIsEditing] = useState(props.isEditable ?? false);
	const [preview, setPreview] = useState<string | null>(null);
	const [loading, setLoading] = useState(false);
	const inputRefs = useRef<
		Record<string, HTMLInputElement | HTMLTextAreaElement | null>
	>({});
	const imageRef = useRef<HTMLImageElement | null>(null);

	const form = useForm<UserProfile>({
		initialValues: {
			firstName: "",
			secondName: "",
			image: "",
			mobile: "",
			address: "",
			city: "",
			introduction: "",
			quote: "",
			joy: "",
			contentLinks: [],
			currentOrganization: "",
			twitter: "",
			instagram: "",
			linkedIn: "",
			otherSocialHandles: [],
			middleName: "",
			email: "",
			role: 3,
			pincode: "",
			...props.initialValues,
		},
		validate: {
			firstName: (value: string) =>
				value.trim() ? null : "First Name is required",

			secondName: (value: string) =>
				value.trim() ? null : "Last Name is required",
			currentOrganization: (value: string) =>
				value.trim() ? null : "Current Organization is required",
			city: (value: string) => (value.trim() ? null : "City is required"),
			address: (value: string) =>
				value.trim() ? null : "Address is required",
			pincode: (value: string, values: UserProfile) => {
				if (!value.trim()) {
					return "Pincode is required";
				}
				if (values.mobile && values.mobile.startsWith("+91")) {
					if (!pincodeList.includes(values.pincode.trim())) {
						return "Invalid Pincode";
					}
				}
				return null;
			},
			introduction: (value: string) => {
				const trimmed = value.trim();
				if (!trimmed) return "Introduction is required";
				const wordCount = trimmed.split(/\s+/).length;
				return wordCount <= 100
					? null
					: "Introduction must not exceed 100 words";
			},
			quote: (value: string) => {
				const trimmed = value.trim();
				if (!trimmed) return "Quote is required";
				const lineCount = trimmed.split("\n").length;
				const wordCount = trimmed.split(/\s+/).length;
				if (wordCount > 100) {
					return "Quote must not exceed 100 words";
				}
				return lineCount <= 2 ? null : "Quote must not exceed 2 lines";
			},

			joy: (value: string) => {
				const trimmed = value.trim();
				if (!trimmed) return "Joy is required";
				const lineCount = trimmed.split("\n").length;
				const wordCount = trimmed.split(/\s+/).length;
				if (wordCount > 100) {
					return " What fills you with joy must not exceed 100 words";
				}
				return lineCount <= 2
					? null
					: "What fills you with joy must not exceed 2 lines";
			},
			image: (value: string | File | null) => {
				if (!value) return "Image is required";
				if (typeof value === "string") {
					return value.trim() === "" ? "Image is required" : null;
				}
				if (value instanceof File) {
					return null;
				}
				return "Image is required";
			},
			twitter: (value: string) => {
				if (!value.trim()) return null;
				try {
					// new URL(value.trim()); // reason for comment: valid URL like www.google.com is not allowed in new URL
					return null;
				} catch {
					return "Enter a valid Twitter URL";
				}
			},

			instagram: (value: string) => {
				if (!value.trim()) return null;
				try {
					// new URL(value.trim()); // reason for comment: valid URL like www.google.com is not allowed in new URL
					return null;
				} catch {
					return "Enter a valid Instagram URL";
				}
			},

			linkedIn: (value: string) => {
				if (!value.trim()) return null;
				try {
					// new URL(value.trim()); // reason for comment: valid URL like www.google.com is not allowed in new URL
					return null;
				} catch {
					return "Enter a valid LinkedIn URL";
				}
			},

			otherSocialHandles: (value: string[]) => {
				if (!value || value.length === 0) return null;
				for (const link of value) {
					if (!link.trim()) continue;
					try {
						//new URL(link.trim()); // reason for comment: valid URL like www.google.com is not allowed in new URL
					} catch {
						return "Enter valid URLs for social handles";
					}
				}
				return null;
			},

			contentLinks: (value: string[]) => {
				if (!value || value.length === 0) return null;
				for (const link of value) {
					if (!link.trim()) continue;
					try {
						//new URL(link.trim()); // reason for comment: valid URL like www.google.com is not allowed in new URL
					} catch {
						return "Enter valid URLs for content links";
					}
				}
				return null;
			},
		},
		transformValues: values => ({
			...values,
			firstName: values.firstName.trim(),
			secondName: values.secondName.trim(),
			middleName: values.middleName.trim(),
			image:
				typeof values.image === "string"
					? values.image.trim()
					: values.image,
			mobile: values.mobile.trim(),
			address: values.address.trim(),
			city: values.city.trim(),
			introduction: values.introduction.trim(),
			quote: values.quote.trim(),
			joy: values.joy.trim(),
			currentOrganization: values.currentOrganization.trim(),
			twitter: values.twitter.trim(),
			instagram: values.instagram.trim(),
			linkedIn: values.linkedIn.trim(),
			email: values.email.trim(),
			contentLinks: values.contentLinks.filter(
				link => link.trim() !== ""
			),
			otherSocialHandles: values.otherSocialHandles.filter(
				handle => handle.trim() !== ""
			),
		}),
	});

	const handleError = (errors: typeof form.errors) => {
		const firstErrorField = Object.keys(errors)[0];
		if (firstErrorField && inputRefs.current[firstErrorField]) {
			inputRefs.current[firstErrorField]?.scrollIntoView({
				behavior: "smooth",
				block: "center",
			});

			inputRefs.current[firstErrorField]?.focus({ preventScroll: true });
		}
	};

	const fetchProfile = async () => {
		setLoading(true);
		try {
			const response = await apiClient<UserProfile>(
				"/api/users/user-profile"
			);
			const data = response.data;

			const getArray = (
				value: string | string[] | undefined | null
			): string[] => {
				if (Array.isArray(value)) {
					return value;
				}
				if (typeof value === "string" && value.trim().startsWith("[")) {
					try {
						const parsed = JSON.parse(value);
						return Array.isArray(parsed) ? parsed : [];
					} catch (e) {
						console.log(e);
						return [];
					}
				}
				return [];
			};

			const profileData: UserProfile = {
				firstName: data.firstName ?? "",
				secondName: data.secondName ?? "",
				image: data.image ?? "",
				mobile: data.mobile ?? "",
				address: data.address ?? "",
				city: data.city ?? "",
				introduction: data.introduction ?? "",
				quote: data.quote ?? "",
				joy: data.joy ?? "",
				contentLinks: getArray(data.contentLinks),
				currentOrganization: data.currentOrganization ?? "",
				twitter: data.twitter ?? "",
				instagram: data.instagram ?? "",
				linkedIn: data.linkedIn ?? "",
				otherSocialHandles: getArray(data.otherSocialHandles),
				middleName: data.middleName ?? "",
				email: data.email ?? "",
				role: data.role ?? 3,
				pincode: data.pincode ?? "",
			};

			form.setValues(profileData);
			form.setInitialValues(profileData);
		} catch (err) {
			console.error("err: ", err);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		if (!props.initialValues) {
			fetchProfile();
		}
	}, []);

	const { setHasUnsavedChanges } = props;
	const isDirty = form.isDirty();

	useEffect(() => {
		if (setHasUnsavedChanges) {
			setHasUnsavedChanges(isDirty);
		}
	}, [isDirty, setHasUnsavedChanges]);

	const renderField = (
		label: string,
		field: keyof UserProfile,
		icon?: React.ReactNode,
		disabled?: boolean,
		options?: { textarea?: boolean; placeholder?: string },
		isRequired?: boolean
	) => {
		const isLinkField = ["twitter", "instagram", "linkedIn"].includes(
			field
		);
		const value = form.values[field] as string;
		const SPOILER_FIELDS = ["introduction", "quote", "joy"];
		const useSpoiler = SPOILER_FIELDS.includes(field);

		return isEditing ? (
			options?.textarea ? (
				<Textarea
					disabled={disabled}
					required={isRequired}
					label={label}
					autosize={false}
					leftSection={icon}
					size="sm"
					radius="md"
					className="text-sm"
					placeholder={options?.placeholder}
					{...form.getInputProps(field)}
					error={form.errors[field] ? form.errors[field] : undefined}
					styles={{
						input: {
							height: "140px",
						},
						section: {
							display: "flex",
							alignItems: "flex-start",
							justifyContent: "center",
							marginTop: "0.45rem",
						},
					}}
					ref={el => {
						inputRefs.current[field] = el;
					}}
				/>
			) : (
				<TextInput
					disabled={disabled}
					required={isRequired}
					label={label}
					leftSection={icon}
					size="sm"
					radius="md"
					className="text-sm"
					placeholder={options?.placeholder}
					{...(field === "role"
						? {
								value: rolesLabelMap[
									roleLabels[
										value as unknown as keyof typeof roleLabels
									]
								],
							}
						: form.getInputProps(field))}
					error={form.errors[field] ? form.errors[field] : undefined}
					ref={el => {
						inputRefs.current[field] = el;
					}}
				/>
			)
		) : (
			<div className="flex items-start gap-3 w-full max-w-full">
				<div className="mt-0.5 text-gray-500 shrink-0">{icon}</div>

				<div className="w-full overflow-hidden">
					<p className="text-sm text-gray-500 font-medium mb-1">
						{label}
					</p>
					{isLinkField ? (
						value && (value as string).trim() !== "" ? (
							<a
								href={
									value.includes("://")
										? value
										: `https://${value}`
								}
								target="_blank"
								rel="noopener noreferrer"
								className="text-blue-600 text-md hover:text-blue-800 truncate block w-full"
							>
								{value}
							</a>
						) : (
							<span className="text-gray-500 truncate block w-full">
								-
							</span>
						)
					) : useSpoiler ? (
						<Spoiler
							maxHeight={80}
							showLabel="Show more"
							hideLabel="Show less"
							styles={{
								control: {
									fontSize: "14px",
								},
							}}
						>
							<p className="text-md text-gray-800 whitespace-pre-wrap break-words">
								{value && (value as string).trim() !== ""
									? value
									: "-"}
							</p>
						</Spoiler>
					) : (
						<p
							className={`text-md text-gray-800 w-full ${
								options?.textarea
									? "whitespace-pre-wrap break-words"
									: "truncate overflow-hidden whitespace-nowrap"
							}`}
						>
							{typeof value === "string" &&
							(value as string).trim() !== ""
								? value
								: typeof value === "number"
									? rolesLabelMap[
											roleLabels[
												value as keyof typeof roleLabels
											]
										]
									: "-"}
						</p>
					)}
				</div>
			</div>
		);
	};

	const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];
		if (file) {
			if (!file.type.startsWith("image/")) {
				notifications.show({
					title: "Invalid Image",
					message: "Please upload a valid image file.",
					color: "red",
				});
				e.target.value = "";
				return;
			}

			const maxSizeInBytes = IMAGE_UPLOAD_MAX_SIZE_IN_MB * 1024 * 1024;
			if (file.size > maxSizeInBytes) {
				notifications.show({
					title: "Image Size Exceeded",
					message: `Image size should not exceed ${IMAGE_UPLOAD_MAX_SIZE_IN_MB}MB.`,
					color: "red",
				});
				e.target.value = "";
				return;
			}

			form.setFieldValue("image", file);
			setPreview(URL.createObjectURL(file));
		}
	};

	const handleSave = async () => {
		const validation = form.validate();

		if (validation.errors.image && imageRef.current) {
			imageRef.current.scrollIntoView({
				behavior: "smooth",
				block: "center",
			});
			return;
		}

		if (validation.hasErrors) {
			handleError(validation.errors);
			return;
		}

		const formData = new FormData();
		Object.entries(form.getTransformedValues()).forEach(([key, value]) => {
			if (value === undefined || value === null) return;

			if (
				Array.isArray(value) ||
				(typeof value === "object" && key !== "image")
			) {
				formData.append(key, JSON.stringify(value));
			} else if (key === "image" && value instanceof File) {
				formData.append("image", value);
			} else {
				formData.append(key, value as string);
			}
		});

		if (Object.keys(form.errors).length > 0) {
			return;
		}

		try {
			const response = await apiClient.post(
				props.saveUrl || "/api/users/update-profile",
				formData,
				{
					headers: {
						"Content-Type": "multipart/form-data",
					},
				}
			);
			notifications.show({
				title: "Updated",
				message: response.data.message,
				color: "green",
				icon: <IconCheck />,
			});
			if (!props.isEditable) {
				setIsEditing(false);
			}

			if (props.fetchProfile) {
				await props.fetchProfile();
			} else if (!props.initialValues) {
				await fetchProfile();
			}
			props.onStepDone?.();
		} catch (err) {
			console.error("err: ", err);
			if (isAxiosError(err)) {
				notifications.show({
					title: "Failed",
					message:
						err?.response?.data?.message ??
						err?.message ??
						"Failed to update profile",
					color: "red",
					icon: <IconX />,
				});
			} else {
				notifications.show({
					title: "Failed",
					message: "Failed to update profile",
					color: "red",
					icon: <IconX />,
				});
			}
		}
	};

	const imageSrc = useMemo(() => {
		return (
			preview ||
			(form.values.image instanceof File
				? URL.createObjectURL(form.values.image)
				: form.values.image
					? `${import.meta.env.VITE_API_URL}${form.values.image}`
					: fallbackImage({
							firstName: form.values.firstName,
							lastName: form.values.secondName,
						}))
		);
	}, [
		preview,
		form.values.image,
		form.values.firstName,
		form.values.secondName,
	]);

	if (loading) {
		return <FullScreenLoader />;
	}

	return (
		<div className="min-h-screen">
			<Paper p="lg">
				<div className="flex justify-end pb-6">
					<div className="flex gap-2">
						{!props.noEditInProfile && !isEditing && (
							<Button
								onClick={() => setIsEditing(true)}
								leftSection={<Edit size={16} />}
							>
								Edit
							</Button>
						)}

						{!props.hideCancelButton && isEditing && (
							<Button
								variant="outline"
								onClick={() => {
									setIsEditing(false);
									form.reset();
									if (preview) {
										URL.revokeObjectURL(preview);
										setPreview(null);
									}
									if (fileRef.current) {
										fileRef.current.value = "";
									}
								}}
							>
								Cancel
							</Button>
						)}
					</div>
				</div>

				<div className="grid grid-cols-1 lg:grid-cols-3 gap-10 pb-10 pt-0">
					<div className="space-y-8">
						<div className="relative w-40 h-40 rounded-full text-center mx-auto group">
							{isEditing && (
								<span className="absolute -right-5  text-red-500 text-2xl">
									*
								</span>
							)}

							<Image
								ref={imageRef}
								src={imageSrc}
								alt="Profile"
								height={160}
								fit="cover"
								radius="xl"
								className="w-full h-full object-cover"
							/>

							{isEditing && (
								<>
									<div
										onClick={() => fileRef.current?.click()}
										className="absolute inset-0 bg-black/40 text-white flex items-center justify-center opacity-0 group-hover:opacity-100 cursor-pointer transition duration-200"
									>
										<Upload size={24} />
									</div>
									<input
										ref={fileRef}
										type="file"
										accept="image/*"
										onChange={handleImageChange}
										className="hidden"
										required
									/>
								</>
							)}
							{form.errors.image && (
								<Text c="red" size="sm" mt={4}>
									{form.errors.image}
								</Text>
							)}
						</div>

						<div className="space-y-4">
							<Divider
								labelPosition="left"
								label={<Text size="sm">Current</Text>}
							/>
							<div className="space-y-6">
								{renderField(
									"Organization",
									"currentOrganization",
									<IconBuilding size={16} />,
									false,
									undefined,
									true
								)}
							</div>
							<div className="space-y-6">
								{renderField(
									"Role",
									"role",
									<IconUserCheck size={16} />,
									true,
									undefined,
									true
								)}
							</div>
						</div>

						<div>
							<Stack>
								<Divider
									labelPosition="left"
									label={
										<Text size="sm">
											Account Information
										</Text>
									}
								/>
								{renderField(
									"Twitter",
									"twitter",
									<IconBrandTwitter size={16} />,
									false,
									{
										placeholder:
											"e.g.,  https://x.com/username",
									}
								)}
								{renderField(
									"Instagram",
									"instagram",
									<IconBrandInstagram size={16} />,
									false,
									{
										placeholder:
											"e.g.,  https://instagram.com/username",
									}
								)}
								{renderField(
									"LinkedIn",
									"linkedIn",
									<IconBrandLinkedin size={16} />,
									false,
									{
										placeholder:
											"e.g.,  https://linkedin.com/in/username",
									}
								)}

								<div className="space-y-4">
									<Box
										size="sm"
										fw={500}
										className="mt-4 flex items-center gap-4 text-gray-600"
									>
										<IconPageBreak size={16} />
										<Text fz={15}>
											Other Social Handles
										</Text>
									</Box>
									{isEditing ? (
										<Stack gap="md">
											{form.values.otherSocialHandles &&
												form.values.otherSocialHandles
													.length > 0 &&
												form.values.otherSocialHandles.map(
													(_, index) => (
														<Group
															key={index}
															gap="sm"
															align="center"
														>
															<TextInput
																leftSection={
																	<IconLink
																		size={
																			16
																		}
																	/>
																}
																placeholder="e.g., https://example.com/username"
																size="sm"
																radius="md"
																className="flex-1"
																variant="filled"
																{...form.getInputProps(
																	`otherSocialHandles.${index}`
																)}
															/>
															<ActionIcon
																color="red"
																variant="subtle"
																onClick={() =>
																	form.removeListItem(
																		"otherSocialHandles",
																		index
																	)
																}
																size="md"
																radius="md"
																aria-label="Remove link"
															>
																<IconTrash
																	size={18}
																/>
															</ActionIcon>
														</Group>
													)
												)}
											{form.errors.otherSocialHandles && (
												<Text c="red" size="sm" mt={4}>
													{
														form.errors
															.otherSocialHandles
													}
												</Text>
											)}
											<ActionIcon
												color="blue"
												variant="filled"
												onClick={() => {
													form.insertListItem(
														"otherSocialHandles",
														""
													);
												}}
												size="lg"
												radius="md"
												className="w-fit self-start"
												aria-label="Add new link"
											>
												<IconPlus size={20} />
											</ActionIcon>
										</Stack>
									) : (
										<List
											spacing="sm"
											size="sm"
											icon={
												<IconLink
													size={16}
													className="text-gray-500"
												/>
											}
											className="text-gray-700"
										>
											{form.values.otherSocialHandles &&
											form.values.otherSocialHandles
												.length > 0 ? (
												form.values.otherSocialHandles.map(
													(
														link: string,
														index: number
													) => (
														<List.Item key={index}>
															<a
																href={link}
																target="_blank"
																rel="noopener noreferrer"
																className="text-sm text-blue-600 hover:underline truncate block max-w-[300px]"
															>
																{link &&
																link.trim() !==
																	""
																	? link
																	: "-"}
															</a>
														</List.Item>
													)
												)
											) : (
												<Text
													size="sm"
													c="dimmed"
													className="text-gray-500"
												>
													No other Social Handles
													available
												</Text>
											)}
										</List>
									)}
								</div>
								<div className="space-y-4">
									<Box
										size="sm"
										fw={500}
										className="mt-4 flex items-center gap-4 text-gray-600"
									>
										<IconPageBreak size={16} />
										<Text fz={15}>Content Links</Text>
									</Box>
									{isEditing ? (
										<Stack gap="md">
											{form.values.contentLinks &&
												form.values.contentLinks
													.length > 0 &&
												form.values.contentLinks.map(
													(_link, index) => (
														<Group
															key={index}
															gap="sm"
															align="center"
														>
															<TextInput
																leftSection={
																	<IconLink
																		size={
																			16
																		}
																	/>
																}
																placeholder="e.g., https://blog.com/post"
																size="sm"
																radius="md"
																className="flex-1"
																variant="filled"
																{...form.getInputProps(
																	`contentLinks.${index}`
																)}
															/>
															<ActionIcon
																color="red"
																variant="subtle"
																onClick={() =>
																	form.removeListItem(
																		"contentLinks",
																		index
																	)
																}
																size="md"
																radius="md"
																aria-label="Remove link"
															>
																<IconTrash
																	size={18}
																/>
															</ActionIcon>
														</Group>
													)
												)}
											{form.errors.contentLinks && (
												<Text c="red" size="sm" mt={4}>
													{form.errors.contentLinks}
												</Text>
											)}
											<ActionIcon
												color="blue"
												variant="filled"
												onClick={() =>
													form.insertListItem(
														"contentLinks",
														""
													)
												}
												size="lg"
												radius="md"
												className="w-fit self-start"
												aria-label="Add new link"
											>
												<IconPlus size={20} />
											</ActionIcon>
										</Stack>
									) : (
										<List
											spacing="sm"
											size="sm"
											icon={
												<IconLink
													size={16}
													className="text-gray-500"
												/>
											}
											className="text-gray-700"
										>
											{form.values.contentLinks &&
											form.values.contentLinks.length >
												0 ? (
												form.values.contentLinks.map(
													(
														link: string,
														index: number
													) => (
														<List.Item key={index}>
															<a
																href={link}
																target="_blank"
																rel="noopener noreferrer"
																className="text-sm text-blue-600 hover:underline truncate block max-w-[300px]"
															>
																{link &&
																link.trim() !==
																	""
																	? link
																	: "-"}
															</a>
														</List.Item>
													)
												)
											) : (
												<Text
													size="sm"
													c="dimmed"
													className="text-gray-500"
												>
													No links available
												</Text>
											)}
										</List>
									)}
								</div>
							</Stack>
						</div>
					</div>

					<div className="lg:col-span-2 space-y-6">
						<Divider
							labelPosition="left"
							label={<Text size="sm">Personal Information</Text>}
						/>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							{renderField(
								"First Name",
								"firstName",
								<User size={16} />,
								false,
								{ placeholder: "e.g.,  John" },
								true
							)}
							{renderField(
								"Middle Name",
								"middleName",
								<User size={16} />,
								false,
								{ placeholder: "e.g.,  Fitzgerald" }
							)}
							{renderField(
								"Last Name",
								"secondName",
								<User size={16} />,
								false,
								{ placeholder: "e.g.,  Kennedy" },
								true
							)}
						</div>

						<div className="space-y-6">
							<Stack>
								<Divider
									labelPosition="left"
									label={
										<Text size="sm">Contact Details</Text>
									}
								/>
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									{renderField(
										"Email",
										"email",
										<IconMail size={16} />,
										true,
										{
											placeholder:
												"e.g.,  <EMAIL>",
										},
										true
									)}
									{renderField(
										"Mobile",
										"mobile",
										<Phone size={16} />,
										true,
										{ placeholder: "e.g.,  +************" },
										true
									)}
									{renderField(
										"Current City",
										"city",
										<MapPin size={16} />,
										false,
										{
											placeholder:
												"e.g., Bangalore, India",
										},
										true
									)}
									{renderField(
										"Pincode",
										"pincode",
										<IconHash size={16} />,
										false,
										{ placeholder: "e.g., 560001" },
										true
									)}
								</div>

								{renderField(
									"Address",
									"address",
									<IconHome size={16} />,
									false,
									{
										placeholder:
											"e.g., 123 Main St, Bangalore, India",
									},
									true
								)}
							</Stack>

							<Divider
								labelPosition="left"
								label={
									<Text size="sm">Something About Me</Text>
								}
							/>
							<Container>
								<Stack gap="lg">
									{renderField(
										"Introduction",
										"introduction",
										<IconId size={16} />,
										false,
										{
											textarea: true,
											placeholder:
												"Your introduction in 4-5 sentences or 100 words.",
										},
										true
									)}

									{renderField(
										"A quote that inspires me",
										"quote",
										<Quote size={16} />,
										false,
										{
											textarea: true,
											placeholder:
												"This quote will be displayed as a part of your profile. Please provide the full quote along with the source.",
										},
										true
									)}
									{renderField(
										"What fills you with joy, outside your work?",
										"joy",
										<Smile size={16} />,
										false,
										{
											textarea: true,
											placeholder:
												"Keep this short and concise not more than 2 lines.",
										},
										true
									)}
								</Stack>
							</Container>
						</div>
					</div>
				</div>
				<Group justify="flex-end" align="center">
					{!props.noEditInProfile && isEditing && (
						<Button
							onClick={handleSave}
							leftSection={<Save size={16} />}
						>
							Save
						</Button>
					)}
				</Group>
			</Paper>
		</div>
	);
};

export default Profile;
